<!DOCTYPE html>
<html lang="sk">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><PERSON>rva<PERSON><PERSON></title>

  <!-- PWA Meta Tags -->
  <meta name="theme-color" content="#673AB7" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="default" />
  <meta name="apple-mobile-web-app-title" content="Krvavý Dobšinský" />

  <!-- Icons -->
  <link rel="apple-touch-icon" href="https://i.imgur.com/F9fKCIr.png" />
  <link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16" />

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@400;700&family=Inter:wght@400;500;600;700&family=Lora:ital,wght@0,400;0,700;1,400;1,700&family=Spectral:wght@400;700&display=swap" rel="stylesheet" />

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Custom CSS -->
  <link rel="stylesheet" href="/assets/main-D-sgq-w0.css" />

  <style>
    /* Critical CSS for loading screen */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Creepster', cursive, system-ui, -apple-system, sans-serif;
      background: linear-gradient(135deg, #2C1A0C 0%, #1A0F06 100%);
      color: #F5E6D3;
      overflow-x: hidden;
      min-height: 100vh;
    }

    #app {
      min-height: 100vh;
      position: relative;
    }

    /* Loading Screen Styles */
    #loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #2C1A0C 0%, #1A0F06 100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      color: #F5E6D3;
    }

    .loading-content {
      text-align: center;
      max-width: 90%;
    }

    .loading-title {
      font-size: clamp(2rem, 8vw, 4rem);
      font-weight: bold;
      margin-bottom: 1rem;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
      color: #DAA520;
      animation: pulse 2s ease-in-out infinite;
    }

    .loading-subtitle {
      font-size: clamp(1rem, 4vw, 1.5rem);
      margin-bottom: 2rem;
      opacity: 0.8;
    }

    .loading-spinner {
      width: 60px;
      height: 60px;
      border: 4px solid rgba(218, 165, 32, 0.3);
      border-top: 4px solid #DAA520;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }

    .loading-text {
      font-size: 1rem;
      opacity: 0.7;
      animation: fadeInOut 2s ease-in-out infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }

    @keyframes fadeInOut {
      0%, 100% { opacity: 0.7; }
      50% { opacity: 1; }
    }

    /* Fallback Content Styles */
    #fallback-content {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #2C1A0C 0%, #1A0F06 100%);
      color: #F5E6D3;
      padding: 2rem;
      text-align: center;
      z-index: 10000;
      overflow-y: auto;
    }

    .fallback-title {
      font-size: 2rem;
      color: #DAA520;
      margin-bottom: 1rem;
    }

    .fallback-message {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      line-height: 1.6;
    }

    .fallback-actions {
      margin-top: 2rem;
    }

    .fallback-button {
      background: #DAA520;
      color: #2C1A0C;
      border: none;
      padding: 1rem 2rem;
      font-size: 1rem;
      border-radius: 8px;
      cursor: pointer;
      margin: 0.5rem;
      font-weight: bold;
      transition: all 0.3s ease;
    }

    .fallback-button:hover {
      background: #B8941C;
      transform: translateY(-2px);
    }

    #debug-info {
      margin-top: 2rem;
      padding: 1rem;
      background: rgba(0,0,0,0.3);
      border-radius: 8px;
      font-family: monospace;
      font-size: 0.9rem;
      text-align: left;
    }
  </style>
</head>
<body class="font-sans antialiased">
  <div class="min-h-screen flex flex-col bg-background text-foreground">
    <!-- Header -->
    <header class="py-3 px-4 md:px-6 bg-primary text-primary-foreground sticky top-0 z-40 shadow-lg">
      <div class="container mx-auto flex justify-between items-center">
        <div class="flex items-center gap-2 sm:gap-3">
          <a aria-label="Domov - Krvavý Dobšinský" class="flex items-center" href="/">
            <img alt="Domov - Krvavý Dobšinský" loading="lazy" width="32" height="32" decoding="async"
                 class="rounded-sm object-contain" src="https://i.imgur.com/F9fKCIr.png"/>
          </a>
          <h1 class="text-xl sm:text-2xl font-bold font-headline ml-1 sm:ml-0">Krvavý Dobšinský</h1>
        </div>
        <div class="flex items-center">
          <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent h-10 w-10 text-primary-foreground hover:text-accent">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.3-4.3"></path>
            </svg>
            <span class="sr-only">Hľadať</span>
          </button>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="flex-grow transition-opacity duration-500 max-w-[375px] mx-auto w-full bg-[#d3d3d3] rounded-[2px] shadow-lg my-3 overflow-y-auto pb-16">
      <div class="space-y-3 p-4 md:p-6" id="episodes-container">
        <!-- Episodes will be loaded here -->
        <div class="w-full p-4 md:p-5 bg-card shadow-lg rounded-xl">
          <div class="flex flex-col md:flex-row gap-4 sm:gap-5">
            <div class="w-full md:w-48 flex-shrink-0">
              <div class="animate-pulse aspect-square w-full rounded-lg bg-muted/50"></div>
            </div>
            <div class="flex-grow md:w-2/3 flex flex-col">
              <div class="animate-pulse rounded-md h-3 w-1/3 mb-2 bg-muted/50"></div>
              <div class="animate-pulse rounded-md h-7 w-3/4 mb-2 bg-muted/50"></div>
              <div class="animate-pulse h-11 w-full sm:max-w-xs rounded-xl bg-muted/50 mt-auto"></div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Bottom Navigation -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 z-50">
      <div class="flex justify-around items-center max-w-md mx-auto">
        <a href="/" class="flex flex-col items-center py-2 px-3 text-primary">
          <svg class="w-6 h-6 mb-1" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
          </svg>
          <span class="text-xs font-medium">Podcast</span>
        </a>
        <a href="/read" class="flex flex-col items-center py-2 px-3 text-gray-500">
          <svg class="w-6 h-6 mb-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z" clip-rule="evenodd"/>
          </svg>
          <span class="text-xs font-medium">Čítať</span>
        </a>
        <a href="/saved" class="flex flex-col items-center py-2 px-3 text-gray-500">
          <svg class="w-6 h-6 mb-1" fill="currentColor" viewBox="0 0 20 20">
            <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z"/>
          </svg>
          <span class="text-xs font-medium">Uložené</span>
        </a>
        <a href="/bonus" class="flex flex-col items-center py-2 px-3 text-gray-500">
          <svg class="w-6 h-6 mb-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"/>
          </svg>
          <span class="text-xs font-medium">Bonusy</span>
        </a>
      </div>
    </nav>
  </div>

  <!-- Tailwind Config -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#673AB7',
            'primary-foreground': '#ffffff',
            background: '#ffffff',
            foreground: '#000000',
            card: '#ffffff',
            'card-foreground': '#000000',
            muted: '#f5f5f5',
            accent: '#f0f0f0',
            border: '#e5e5e5'
          },
          fontFamily: {
            'headline': ['Cormorant Garamond', 'serif'],
            'episodeTitle': ['Lora', 'serif'],
            'ui': ['Inter', 'sans-serif']
          }
        }
      }
    }
  </script>

  <!-- Load episodes script -->
  <script>
    // Load RSS feed and display episodes
    async function loadEpisodes() {
      try {
        const response = await fetch('https://corsproxy.io/?https%3A%2F%2Fanchor.fm%2Fs%2F8db2e1ec%2Fpodcast%2Frss');
        const xmlText = await response.text();

        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
        const items = xmlDoc.querySelectorAll('item');

        const container = document.getElementById('episodes-container');
        container.innerHTML = '';

        Array.from(items).slice(0, 10).forEach((item, index) => {
          const title = item.querySelector('title')?.textContent || 'Bez názvu';
          const description = item.querySelector('description')?.textContent || '';
          const audioUrl = item.querySelector('enclosure')?.getAttribute('url') || '';

          const episodeCard = `
            <div class="rounded-lg border text-card-foreground w-full overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200 bg-card border-border/50 flex flex-col">
              <div class="relative w-full bg-muted/30 aspect-square">
                <img alt="${title}" loading="lazy" decoding="async"
                     class="object-contain w-full h-full"
                     src="https://placehold.co/300x300.png"/>
              </div>
              <div class="p-4 md:p-5 flex flex-col flex-grow">
                <h3 class="text-lg sm:text-xl font-episodeTitle font-bold text-foreground mb-2 line-clamp-3 flex-grow">${title}</h3>
                <div class="flex items-center gap-2 mt-auto">
                  <button class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 flex-grow py-3 px-4 sm:px-5 rounded-xl bg-primary text-primary-foreground hover:bg-primary/90 shadow-md text-base font-ui"
                          onclick="playEpisode('${audioUrl}', '${title}')" aria-label="Prehrať epizódu">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 h-5 w-5">
                      <polygon points="6 3 20 12 6 21 6 3"></polygon>
                    </svg>
                    Prehrať epizódu
                  </button>
                </div>
              </div>
            </div>
          `;
          container.innerHTML += episodeCard;
        });

        // Show main content
        document.querySelector('main').style.opacity = '1';

      } catch (error) {
        console.error('Error loading episodes:', error);
        document.getElementById('episodes-container').innerHTML = '<p class="text-center p-4">Chyba pri načítavaní epizód</p>';
      }
    }

    function playEpisode(audioUrl, title) {
      alert(`Prehrávam: ${title}`);
      // Here you would implement actual audio playback
    }

    // Load episodes when page loads
    document.addEventListener('DOMContentLoaded', loadEpisodes);
  </script>

</body>
</html>
